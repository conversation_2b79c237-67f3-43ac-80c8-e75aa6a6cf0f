

// @ts-nocheck
// Motivo: Este componente ainda depende de propriedades dinâmicas provenientes do backend
// e a tipagem Influencer será revisada em etapa posterior. A diretiva evita que
// erros de tipagem interrompam o build enquanto refatoramos.

import type React from "react"

import { useState } from "react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { InfluencerAvatar } from "@/components/ui/influencer-avatar"
import { ZeroLCPInfluencerAvatar } from "@/components/ui/zero-lcp-avatar"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from "@/components/ui/dialog"
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select"
import { Bookmark, Copy, Pencil, Trash2, CheckSquare, Square, Mail, Plus, ChevronDown, ChevronUp } from "lucide-react"
import { Protect } from "@clerk/nextjs"
import { SocialIcon } from "@/components/ui/social-icons"
import { Influencer } from "./types"
import { getInfluencerBrands } from "./utils"
import { Campaign } from "@/types/campaign"
import { CampaignService } from "@/services/campaign-service"
import { useCategoriesGraphQL } from "@/hooks/use-categories-graphql"
import { useTranslations } from "@/hooks/use-translations"
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"

interface InfluencerCardProps {
  influencer: Influencer;
  onClick: (influencer: Influencer) => void;
  isSelected: boolean;
  selectionMode: boolean;
  onToggleSelection: (id: string) => void;
  onDuplicate?: (influencer: Influencer) => void;
  onEdit?: (influencer: Influencer) => void;
  onDelete?: (influencer: Influencer) => void;
  isInProposal?: boolean;
  proposalId?: string;
  isSelectedFromUrl?: boolean;
  influencerStatus?: 'pendente' | 'aceito' | 'rejeitado' | 'descartado' | null;
  // 🔗 NOVO: Prop para lista compartilhada
  isSharedList?: boolean;
}

// Interface para a categoria
interface Category {
  id: string;
  name: string;
  slug: string;
  color?: string;
}

// Fix Influencer type issues by extending with index signature
interface InfluencerWithDynamic extends Influencer {
  [key: string]: any;
}

export function InfluencerCard({
  influencer: influencerRaw,
  onClick,
  isSelected,
  selectionMode,
  onToggleSelection,
  onDuplicate,
  onEdit,
  onDelete,
  isInProposal = false,
  proposalId,
  isSelectedFromUrl = false,
  influencerStatus = null,
  // 🔗 NOVO: Prop para lista compartilhada
  isSharedList = false
}: InfluencerCardProps) {
  const { t } = useTranslations();
  const influencer = influencerRaw as InfluencerWithDynamic;

  // Estado para controlar expansão das redes sociais
  const [networksExpanded, setNetworksExpanded] = useState(false);
  
  // Estado para controlar expansão das categorias
  const [categoriesExpanded, setCategoriesExpanded] = useState(false);
  
  // 🔄 REVERTIDO: Voltar a usar GraphQL hook seguro
  const { categories, categoriesLoading: isLoading } = useCategoriesGraphQL(false);
  
  // Estados para campanhas
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [selectedCampaign, setSelectedCampaign] = useState<string>('');
  const [isAddingToCampaign, setIsAddingToCampaign] = useState<boolean>(false);
  const [campaignDialogOpen, setCampaignDialogOpen] = useState<boolean>(false);
  
  // Função para buscar campanhas ativas
  const fetchCampaigns = async () => {
    try {
      const activeCampaigns = await CampaignService.getActiveCampaigns();
      setCampaigns(activeCampaigns);
    } catch (error) {
      console.error('Erro ao buscar campanhas:', error);
    }
  };
  
  // Função para adicionar influenciador à campanha
  const handleAddToCampaign = async () => {
    if (!selectedCampaign) {
      alert(t('influencers.card.select_campaign'));
      return;
    }
    
    setIsAddingToCampaign(true);
    
    try {
      const influencerData = {
        financialId: influencer.financialData?.id || '',
        negotiatedPrice: 0, // Valor a ser negociado
        deliverables: [],
        status: 'invited' as const,
        paymentStatus: 'pending' as const,
        notes: `Influenciador adicionado automaticamente via card`
      };
      
      await CampaignService.addInfluencerToCampaign(
        selectedCampaign,
        String(influencer.id),
        influencerData
      );
      
      alert(t('influencers.card.campaign_added_success'));
      setSelectedCampaign('');
      setCampaignDialogOpen(false);
    } catch (error) {
      console.error('Erro ao adicionar influenciador à campanha:', error);
      alert(t('influencers.card.campaign_add_error'));
    } finally {
      setIsAddingToCampaign(false);
    }
  };
  
  // Carregar campanhas quando o diálogo abrir
  const handleOpenCampaignDialog = () => {
    setCampaignDialogOpen(true);
    fetchCampaigns();
  };
  // Função para formatar número de seguidores (k para milhares, MI para milhões)
  const formatarSeguidores = (numero: string | number): string => {
    const num = typeof numero === 'string' ? parseInt(numero.replace(/[^0-9]/g, '')) : numero;
    
    if (isNaN(num)) return '0';
    
    if (num >= 1000000) {
      return (num / 1000000).toFixed(num % 1000000 === 0 ? 0 : 1).replace('.0', '') + 'MI';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(num % 1000 === 0 ? 0 : 1).replace('.0', '') + 'k';
    } else {
      return num.toString();
    }
  };

  // Função para obter o nome da categoria pelo ID
  const getCategoryNameById = (categoryId: string) => {
    if (!categoryId) return t('influencers.card.no_category');
    
    // Se as categorias ainda estão carregando, mostra um placeholder
    if (isLoading) {
      return t('influencers.card.loading_category');
    }
    
    // Buscar a categoria no array de categorias
    const category = categories.find((cat: Category) => cat.id === categoryId);
    
    if (category) {
      return category.name; // Retorna o nome real da categoria
    }
    
    // Se não encontrou a categoria, retorna o ID formatado como fallback
    return categoryId.charAt(0).toUpperCase() + categoryId.slice(1).toLowerCase();
  };

  // Função para obter o nome da categoria
  const getCategoryName = (influencer: InfluencerWithDynamic) => {
    const catName = influencer.categoryName as string | undefined;
    if (catName) return catName;
    
    // Verificar se tem mainCategories (novo formato)
    if (influencer.mainCategories && influencer.mainCategories.length > 0) {
      const firstCategory = Array.isArray(influencer.mainCategories) 
        ? influencer.mainCategories[0] 
        : influencer.mainCategories;
      return getCategoryNameById(firstCategory);
    }
    
    // Fallback: buscar pelo ID da categoria (para compatibilidade com dados antigos)
    if (!influencer.category) return t('influencers.card.no_category');
    
    return getCategoryNameById(influencer.category);
  };
  
  // Função para obter a cor da categoria
  const getCategoryColor = (categoryId: string) => {
    if (!categoryId) return '#cccccc';
    
    // Buscar a categoria no array de categorias
    const category = categories.find((cat: Category) => cat.id === categoryId);
    
    if (category && category.color) {
      return category.color; // Retorna a cor definida da categoria
    }
    
    
    return "#9810fa"; // Cor padrão
  };

  // 🆕 Ícones customizados para CRM
  const CheckIcon = () => (
    <svg viewBox="0 0 24 24" className="h-4 w-4" fill="currentColor">
      <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
    </svg>
  );

  const CloseIcon = () => (
    <svg viewBox="0 0 24 24" className="h-4 w-4" fill="currentColor">
      <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
    </svg>
  );

  const MinusIcon = () => (
    <svg viewBox="0 0 24 24" className="h-4 w-4" fill="currentColor">
      <path d="M19 13H5v-2h14v2z"/>
    </svg>
  );

  const DotIcon = () => (
    <svg viewBox="0 0 24 24" className="h-4 w-4" fill="currentColor">
      <circle cx="12" cy="12" r="4"/>
    </svg>
  );

  // 🆕 Função para renderizar o status do influencer na proposta
  const renderInfluencerProposalStatus = (status: string | null) => {
        // 🔥 CORRIGIDO: Mostrar badge quando há proposta, mesmo sem status definido
    if (!isInProposal) return null;
    
    // Se não há status definido mas está em proposta, mostrar como pendente
    const finalStatus = status || 'pendente';
    
    // 🔥 DEBUG: Log simplificado (removido para performance)

    const getStatusConfig = (status: string) => {
      switch (status) {
        case 'aceito':
          return {
            icon: CheckIcon,
            label: t('influencers.badges.approved'),
            bgColor: 'bg-gradient-to-r from-[#10fa99] to-[#b8ffd1]',
            textColor: 'text-green-800'
          };
        case 'rejeitado':
          return {
            icon: CloseIcon,
            label: t('influencers.badges.rejected'),
            bgColor: 'bg-gradient-to-r from-[#ff4757] to-[#ffb3ba]',
            textColor: 'text-red-800'
          };
        case 'descartado':
          return {
            icon: MinusIcon,
            label: t('influencers.badges.discarded'),
            bgColor: 'bg-gradient-to-r from-[#57606f] to-[#c7d2fe]',
            textColor: 'text-gray-800'
          };
        default: // 'pendente'
          return {
            icon: DotIcon,
            label: t('influencers.badges.pending'),
            bgColor: 'bg-gradient-to-r from-[#ffa726] to-[#ffcc80]',
            textColor: 'text-orange-800'
          };
      }
    };

    const config = getStatusConfig(finalStatus);
    const IconComponent = config.icon;

    return (
      <Badge 
        className={`${config.bgColor} ${config.textColor} border-none text-xs font-medium transition-all duration-200 hover:scale-105 hover:shadow-sm`}
        title={config.label}
      >
        <IconComponent />
        <span className="ml-1">{config.label}</span>
      </Badge>
    );
  };

  // 🔥 CORREÇÃO: Determinar se o card está selecionado (incluindo seleção via URL)
  const isCardSelected = isSelected || isSelectedFromUrl;

  return (
    <Card
      className={`transition-all duration-200  minmax-w-[17rem]  overflow-hidden relative backdrop-blur-md ${
        selectionMode
          ? (isCardSelected ? 'bg-white/10 border border-[#ff0074] dark:border-[#ff0074]' : 'bg-white  border border-[#ff0074] dark:border-[#ff0074]')
          : (isCardSelected ? 'group   border border-[#ff0074] dark:border-[#ff0074]' : 'bg-white border ')
      }`}
      style={{
        backdropFilter: 'blur(2px)',
        WebkitBackdropFilter: 'blur(2px)'
      }}
      onClick={() => selectionMode ? onToggleSelection(String(influencer.id)) : onClick(influencer)}
    >
      {/* Checkbox de seleção (visível apenas no modo de seleção E não for lista compartilhada) */}
      {selectionMode && !isSharedList && (
        <div 
          className="absolute top-3 right-3 z-10"
          onClick={(e) => {
            e.stopPropagation();
            onToggleSelection(String(influencer.id));
          }}
        >
          {isCardSelected ? (
            <CheckSquare className="h-5 w-5 text-[#ff0074]" />
          ) : (
            <Square className="h-5 w-5 text-muted-foreground" />
          )}
        </div>
      )}

                    {/* 🔥 NOVO: Indicador de seleção via URL */}
      {isSelectedFromUrl && !selectionMode && (
        <div className="absolute top-2 right-2 z-10" title={t('influencers.selected_via_url')}>
          <div className="w-3 h-3 bg-green-500 rounded-full border border-white shadow-sm animate-pulse"></div>
        </div>
      )}

      {/* Conteúdo principal do card */}
      <div className="p-4 flex flex-col">
          {/* Barra de Ícones Superior (Contato e Ações) */}
          <div className="w-full  flex justify-between items-center mb-2">
            {/* Ícones de Contato (Esquerda) - Ocultar para listas compartilhadas */}
            {!isSharedList && (
              <div className="flex items-center space-x-3">
                {/* 🆕 Status da proposta ao lado dos ícones de contato */}
                {renderInfluencerProposalStatus(influencerStatus)}

                {influencer.financialData?.email && (
                  <a href={`mailto:${influencer.financialData.email}`} target="_blank" rel="noopener noreferrer" onClick={(e) => e.stopPropagation()} title={influencer.financialData.email}>
                    <Mail className="w-4 h-4 text-foreground dark:text-white hover:text-foreground dark:hover:text-gray-300 cursor-pointer transition-all duration-200 hover:scale-105 hover:shadow-sm" />
                  </a>
                )}
                <Protect role="org:admin">
                  {influencer.whatsapp && (
                    <a href={`https://wa.me/${influencer.whatsapp.replace(/\D/g, '')}`} target="_blank" rel="noopener noreferrer" onClick={(e) => e.stopPropagation()} title={influencer.whatsapp}>
                      <svg xmlns="http://www.w3.org/2000/svg" shapeRendering="geometricPrecision" textRendering="geometricPrecision" imageRendering="optimizeQuality" viewBox="0 0 510 512.459" className="w-4 h-4 text-neutral-800 dark:text-white hover:text-neutral-600 dark:hover:text-gray-300 cursor-pointer transition-all duration-200 hover:scale-105 hover:shadow-sm">
                        <path fill="currentColor" d="M435.689 74.468C387.754 26.471 324 .025 256.071 0 116.098 0 2.18 113.906 2.131 253.916c-.024 44.758 11.677 88.445 33.898 126.946L0 512.459l134.617-35.311c37.087 20.238 78.85 30.891 121.345 30.903h.109c139.949 0 253.88-113.917 253.928-253.928.024-67.855-26.361-131.645-74.31-179.643v-.012zm-179.618 390.7h-.085c-37.868-.011-75.016-10.192-107.428-29.417l-7.707-4.577-79.886 20.953 21.32-77.889-5.017-7.987c-21.125-33.605-32.29-72.447-32.266-112.322.049-116.366 94.729-211.046 211.155-211.046 56.373.025 109.364 22.003 149.214 61.903 39.853 39.888 61.781 92.927 61.757 149.313-.05 116.377-94.728 211.058-211.057 211.058v.011zm115.768-158.067c-6.344-3.178-37.537-18.52-43.358-20.639-5.82-2.119-10.044-3.177-14.27 3.178-4.225 6.357-16.388 20.651-20.09 24.875-3.702 4.238-7.403 4.762-13.747 1.583-6.343-3.178-26.787-9.874-51.029-31.487-18.86-16.827-31.597-37.598-35.297-43.955-3.702-6.355-.39-9.789 2.775-12.943 2.849-2.848 6.344-7.414 9.522-11.116s4.225-6.355 6.343-10.581c2.12-4.238 1.06-7.937-.522-11.117-1.584-3.177-14.271-34.409-19.568-47.108-5.151-12.37-10.385-10.69-14.269-10.897-3.703-.183-7.927-.219-12.164-.219s-11.105 1.582-16.925 7.939c-5.82 6.354-22.209 21.709-22.209 52.927 0 31.22 22.733 61.405 25.911 65.642 3.177 4.237 44.745 68.318 108.389 95.812 15.135 6.538 26.957 10.446 36.175 13.368 15.196 4.834 29.027 4.153 39.96 2.52 12.19-1.825 37.54-15.353 42.824-30.172 5.283-14.818 5.283-27.529 3.701-30.172-1.582-2.641-5.819-4.237-12.163-7.414l.011-.024z"/>
                      </svg>
                    </a>
                  )}
                </Protect>

                {/* 🔥 NOVO: Badge de snapshot (se aplicável) */}
                {(influencer as any).isSnapshot && (
                  <Badge className="bg-amber-500 text-white text-xs px-2 py-1 flex items-center gap-1 transition-all duration-200 hover:scale-105 hover:shadow-sm">
                    📸 {t('influencers.badges.snapshot')}
                  </Badge>
                )}
              </div>
            )}
 
            {/* Ícones de Ações (Direita) - Ocultar para listas compartilhadas */}
            {!selectionMode && !isSharedList && (
              <div className="flex space-x-2">
              

                {/* 🔒 Bookmark - Apenas admin */}
                <Protect role="org:admin">
                  <Bookmark
                    className="w-4 h-4 text-foreground dark:text-white hover:text-foreground dark:hover:text-gray-300 cursor-pointer transition-all duration-200 hover:scale-105 hover:shadow-sm"
                  />
                </Protect>

                {/* 🔒 Duplicar - Apenas admin */}
                <Protect role="org:admin">
                  <Copy
                    className="w-4 h-4 text-foreground dark:text-white hover:text-foreground dark:hover:text-gray-300 cursor-pointer transition-all duration-200 hover:scale-105 hover:shadow-sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      onDuplicate?.(influencer);
                    }}
                  />
                </Protect>

                {/* 🔒 Editar - Apenas admin */}
                <Protect role="org:admin">
                  <Pencil
                    className="w-4 h-4 text-foreground dark:text-white hover:text-foreground dark:hover:text-gray-300 cursor-pointer transition-all duration-200 hover:scale-105 hover:shadow-sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      onEdit?.(influencer);
                    }}
                  />
                </Protect>

                {/* 🔒 Excluir - Apenas admin */}
                <Protect role="org:admin">
                  <Trash2
                    className="w-4 h-4 text-foreground dark:text-white hover:text-red-500 dark:hover:text-red-400 cursor-pointer transition-all duration-200 hover:scale-105 hover:shadow-sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      onDelete?.(influencer);
                    }}
                  />
                </Protect>
              </div>
            )}
          </div>
        {/* Header com avatar e informações */}
        <div className="flex flex-col items-center w-full">
          {/* Avatar e informações em coluna centralizada */}
          <div className="flex flex-col items-center gap-3 w-full">
            {/* Avatar Otimizado para LCP */}
            <div className="relative">
              <ZeroLCPInfluencerAvatar
                influencerName={influencer.name || "Sem nome"}
                avatarUrl={influencer.avatar}
                className="border-foreground/5"
                size="xl"
              />
              {(influencer.verified || influencer.isVerified) && (
                <div className="absolute -bottom-[-5px] -right-[-3px] bg-transparent text-white rounded-full h-5 w-5 flex items-center justify-center transition-all duration-200 hover:scale-105 hover:shadow-sm">
                  <svg id="Camada_2" data-name="Camada 2" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" viewBox="0 0 156.61 189.98" width="24" height="24">
                    <defs>
                      <linearGradient id="Gradiente_sem_nome_147" data-name="Gradiente sem nome 147" x1="52.12" y1="184.26" x2="115.13" y2="9.61" gradientUnits="userSpaceOnUse">
                        <stop offset="0" stopColor="#ff0074"/>
                        <stop offset="1" stopColor="#ff0074"/>
                      </linearGradient>
                    </defs>
                    <g id="Camada_1-2" data-name="Camada 1">
                      <path className="cls-1" fill="url(#Gradiente_sem_nome_147)" d="m155.35,97.66h0c-.82-4.58-2.05-9.01-3.63-13.28-5.77-15.52-16.32-28.72-29.87-37.79,1.72,4.96,2.66,10.29,2.66,15.84,0,4.5-.61,8.86-1.77,12.99-12.01-8.56-19.95-22.48-20.31-38.27-.01-.39-.02-.78-.02-1.16,0-4.5.61-8.86,1.77-12.99,1.02-3.68,2.46-7.18,4.28-10.44,2.62-4.73,6.01-8.97,10-12.56-9.86.78-19.21,3.38-27.71,7.47-3.97,1.91-7.75,4.15-11.32,6.68-8.21,5.82-15.25,13.19-20.7,21.68-7.82,12.18-12.35,26.68-12.35,42.22,0,3.88.28,7.68.83,11.41.71,4.87,1.87,9.59,3.43,14.12-3.55-2.2-6.8-4.85-9.67-7.87-8.22-8.67-13.26-20.39-13.26-33.29,0-4.04.5-7.96,1.43-11.7C14.54,62.52,4.26,79.45,1.06,98.78c-.7,4.2-1.06,8.51-1.06,12.9,0,43.25,35.06,78.3,78.3,78.3,27.69,0,52.03-14.38,65.95-36.08,7.82-12.19,12.35-26.68,12.35-42.23,0-4.78-.43-9.47-1.25-14.02Zm-77.15,77.38c-17.82,0-33.39-9.63-41.79-23.98,12.07,7.61,26.37,12.01,41.69,12.01,12.16,0,23.68-2.77,33.94-7.72,2.79-1.35,5.48-2.85,8.07-4.49-1.03,1.78-2.17,3.48-3.41,5.11-8.84,11.59-22.79,19.07-38.5,19.07Z"/>
                    </g>
                  </svg>
                </div>
              )}
            </div>

            {/* Informações principais */}
            <div className="flex flex-col justify-center items-center mt-3">
              {/* Nome do influenciador */}
              <h3 className="font-regular text-base text-[#19191e]  dark:text-white">{influencer.name || t('influencers.card.no_name')}</h3>
        
              {/* Localização */}
              <div className="flex items-center justify-center text-sm text-foreground/60 dark:text-gray-400 mt-1">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" className="mr-1 transition-all duration-200 hover:scale-105 hover:shadow-sm" viewBox="0 0 16 16">
                  <path d="M8 16s6-5.686 6-10A6 6 0 0 0 2 6c0 4.314 6 10 6 10m0-7a3 3 0 1 1 0-6 3 3 0 0 1 0 6" />
                </svg>
                {influencer.location || t('influencers.card.no_location')}
              </div>
              
              {/* Categorias - badges com expansão */}
               <div className="flex flex-wrap justify-center gap-1.5 mt-2 mb-2 w-full">
               {/* Exibindo categorias com base no estado de expansão */}
                {(() => {
                  const maxVisible = categoriesExpanded ? Infinity : 2;
                  
                  if (influencer.mainCategoriesData) {
                    return influencer.mainCategoriesData.slice(0, maxVisible).map((category, index) => (
                      <Badge
                        key={index}
                        variant="outline"
                        className={`bg-[#ff0074] text-white dark:text-white border-gray-850 hover:bg-[${getCategoryColor(category.id)}] hover:text-[#ff0074] text-[10px] py-0 px-2 h-5 transition-all duration-200 hover:scale-105 hover:shadow-sm`}
                      >
                        <div className={`w-1.5 h-1.5 bg-[#9810fa] rounded-full mr-1`}></div>
                        {category.name}
                      </Badge>
                    ));
                  } else if (influencer.mainCategories && influencer.mainCategories.length > 0) {
                    return influencer.mainCategories.slice(0, maxVisible).map((categoryId, index) => (
                      <Badge
                        key={index}
                        variant="outline"
                        className={`bg-[#ff0074] text-white dark:text-white border-gray-850 hover:bg-[${getCategoryColor(categoryId)}] hover:text-[#ff0074] text-[10px] py-0 px-2 h-5 transition-all duration-200 hover:scale-105 hover:shadow-sm`}
                      >
                        <div className={`w-1.5 h-1.5 bg-[#9810fa] rounded-full mr-1`}></div>
                        {getCategoryNameById(categoryId)}
                      </Badge>
                    ));
                  } else if (influencer.categories && influencer.categories.length > 0) {
                    return influencer.categories.slice(0, maxVisible).map((catName: string, index: number) => (
                      <Badge
                        key={index}
                        variant="outline"
                        className={`bg-[#ff0074] text-white dark:text-white border-gray-850 hover:bg-[#9810fa] hover:text-[#ff0074] text-[10px] py-0 px-2 h-5 transition-all duration-200 hover:scale-105 hover:shadow-sm`}
                      >
                        <div className={`w-1.5 h-1.5 bg-[#9810fa] rounded-full mr-1`}></div>
                        {catName}
                      </Badge>
                    ));
                  } else if (influencer.category) {
                    return (
                      <Badge
                        variant="outline"
                        className={`bg-[#ff0074] text-white dark:text-white border-gray-850 hover:bg-[#9810fa] hover:text-[#ff0074] text-[10px] py-0 px-2 h-5 transition-all duration-200 hover:scale-105 hover:shadow-sm`}
                      >
                        <div className={`w-1.5 h-1.5 bg-[#9810fa] rounded-full mr-1`}></div>
                        {influencer.category}
                      </Badge>
                    );
                  }
                  return null;
                })()}
                
                {/* Badge +X clicável para expandir/recolher categorias */}
                {(() => {
                  const totalCats = influencer.mainCategoriesData?.length || influencer.mainCategories?.length || influencer.categories?.length || (influencer.category ? 1 : 0);
                  if (totalCats > 2) {
                    return (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Badge 
                            className="text-muted-foreground bg-transparent dark:border-gray-800 border-gray-200 hover:bg-gray-100 cursor-pointer transition-all duration-200 hover:scale-105 hover:shadow-sm text-[10px] py-0 px-2 h-5 flex items-center gap-1"
                            onClick={(e) => {
                              e.stopPropagation();
                              setCategoriesExpanded(!categoriesExpanded);
                            }}
                          >
                            {categoriesExpanded ? (
                              <>
                                <ChevronUp size={8} />
                                Menos
                              </>
                            ) : (
                              <>
                                +{totalCats - 2}
                                <ChevronDown size={8} />
                              </>
                            )}
                          </Badge>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{categoriesExpanded ? 'Mostrar menos categorias' : 'Mostrar todas as categorias'}</p>
                        </TooltipContent>
                      </Tooltip>
                    );
                  }
                  return null;
                })()}
              </div>
              
              {/* Redes sociais */}
              {(() => {
                const availableNetworks = [];
                
                // Debug removido para reduzir logs excessivos
                
                // Instagram - usar campos diretos da nova estrutura
                const instagramFollowers = influencer.instagramFollowers || 
                                          (influencer.instagram && influencer.instagram !== '0' ? parseInt(influencer.instagram, 10) : 0);
                
                if (instagramFollowers > 0) {
                  availableNetworks.push({
                    name: 'instagram',
                    href: `https://instagram.com/${influencer.instagramUsername || ''}`,
                    icon: <SocialIcon platform="instagram" size={18} className="text-current" />,
                    followers: formatarSeguidores(instagramFollowers),
                    label: t('influencers.card.followers_label'),
                    isMain: influencer.mainNetwork === 'instagram'
                  });
                }
                
                // YouTube - usar campos diretos da nova estrutura
                const youtubeFollowers = influencer.youtubeFollowers || 
                                       (influencer.youtube && influencer.youtube !== '0' ? parseInt(influencer.youtube, 10) : 0);
                
                if (youtubeFollowers > 0) {
                  availableNetworks.push({
                    name: 'youtube',
                    href: `https://youtube.com/@${influencer.youtubeUsername || ''}`,
                    icon: <SocialIcon platform="youtube" size={18} className="text-current" />,
                    followers: formatarSeguidores(youtubeFollowers),
                    label: t('influencers.card.subscribers_label'),
                    isMain: influencer.mainNetwork === 'youtube'
                  });
                }
                
                // TikTok - usar campos diretos da nova estrutura
                const tiktokFollowers = influencer.tiktokFollowers || 
                                      (influencer.tiktok && influencer.tiktok !== '0' ? parseInt(influencer.tiktok, 10) : 0);
                
                if (tiktokFollowers > 0) {
                  availableNetworks.push({
                    name: 'tiktok',
                    href: `https://tiktok.com/@${influencer.tiktokUsername || ''}`,
                    icon: <SocialIcon platform="tiktok" size={18} className="text-current" />,
                    followers: formatarSeguidores(tiktokFollowers),
                    label: t('influencers.card.followers_label'),
                    isMain: influencer.mainNetwork === 'tiktok'
                  });
                }
                
                // Facebook - usar campos diretos da nova estrutura
                const facebookFollowers = influencer.facebookFollowers || 
                                        parseInt(influencer.facebook || '0', 10);
                if (facebookFollowers > 0) {
                  availableNetworks.push({
                    name: 'facebook',
                    href: `https://facebook.com/${influencer.facebookUsername || ''}`,
                    icon: <SocialIcon platform="facebook" size={18} className="text-current" />,
                    followers: formatarSeguidores(facebookFollowers),
                    label: t('influencers.card.followers_label'),
                    isMain: influencer.mainNetwork === 'facebook'
                  });
                }
                
                // Twitch - usar campos diretos da nova estrutura
                const twitchFollowers = influencer.twitchFollowers || 
                                      parseInt(influencer.twitch || '0', 10);
                if (twitchFollowers > 0) {
                  availableNetworks.push({
                    name: 'twitch',
                    href: `https://twitch.tv/${influencer.twitchUsername || ''}`,
                    icon: <SocialIcon platform="twitch" size={18} className="text-current" />,
                    followers: formatarSeguidores(twitchFollowers),
                    label: t('influencers.card.followers_label'),
                    isMain: influencer.mainNetwork === 'twitch'
                  });
                }
                
                // Kwai - usar campos diretos da nova estrutura
                const kwaiFollowers = influencer.kwaiFollowers || 
                                    parseInt(influencer.kwai || '0', 10);
                if (kwaiFollowers > 0) {
                  availableNetworks.push({
                    name: 'kwai',
                    href: `https://kwai.com/${influencer.kwaiUsername || ''}`,
                    icon: <SocialIcon platform="kwai" size={18} className="text-current" />,
                    followers: formatarSeguidores(kwaiFollowers),
                    label: t('influencers.card.followers_label'),
                    isMain: influencer.mainNetwork === 'kwai'
                  });
                }
                
                const networkCount = availableNetworks.length;
                const hasMoreThan3Networks = networkCount > 3;
                const visibleNetworks = networksExpanded ? availableNetworks : availableNetworks.slice(0, 3);
                
                // Sempre usar grid-cols-3 para no máximo 3 colunas
                let gridCols = 'grid-cols-1';
                if (networkCount === 2) gridCols = 'grid-cols-2';
                else if (networkCount >= 3) gridCols = 'grid-cols-3';
                
                return availableNetworks.length > 0 ? (
                  <div className="mt-3">
                    <div className={`grid ${gridCols} gap-4 transition-all duration-300`}>
                      {visibleNetworks.map((network) => (
                      <a 
                        key={network.name}
                        href={network.href}
                        target="_blank" 
                        rel="noopener noreferrer"
                        className={`flex flex-col items-center hover:scale-105 hover:shadow-sm transition-all duration-200 cursor-pointer relative ${
                          network.isMain ? 'transform scale-105' : ''
                        }`}
                        onClick={(e) => e.stopPropagation()}
                      >
                        {/* Badge de rede principal com tooltip */}
                        {network.isMain && (
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <div className="absolute -top-2 -right-[-0.25rem] z-10">
                                <div className="bg-gradient-to-r from-[#ff0074] to-[#9810fa] text-white p-1 rounded-full shadow-lg transition-all duration-200 hover:scale-105 hover:shadow-sm">
                                  <svg xmlns="http://www.w3.org/2000/svg" width="8" height="8" fill="currentColor" viewBox="0 0 16 16">
                                    <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
                                  </svg>
                                </div>
                              </div>
                            </TooltipTrigger>
                            <TooltipContent className="bg-white text-black shadow-2xl backdrop-blur-none opacity-100">
                              <p className="font-medium">{t('influencers.badges.main_network')}</p>
                            </TooltipContent>
                          </Tooltip>
                        )}
                        
                        {/* Ícone da rede social */}
                        <div>
                          {network.icon}
                        </div>
                        
                        {/* Número de seguidores */}
                        <p className="text-base font-semibold mt-1 transition-all duration-200 hover:scale-105">
                          {network.followers}
                        </p>

                        {/* Label - traduzido dinamicamente */}
                        <p className="text-xs font-medium text-muted-foreground transition-all duration-200 hover:scale-105">
                          {network.name === 'youtube' ? t('influencers.card.subscribers_label') : t('influencers.card.followers_label')}
                        </p>
                      </a>
                    ))}
                    </div>
                    
                    {/* Botão para expandir/recolher redes adicionais */}
                    {hasMoreThan3Networks && (
                      <div className="flex justify-center mt-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            setNetworksExpanded(!networksExpanded);
                          }}
                          className="h-6 px-2 text-xs text-muted-foreground hover:text-foreground transition-all duration-200 hover:scale-105 hover:shadow-sm"
                        >
                          {networksExpanded ? (
                            <>
                              <ChevronUp className="h-3 w-3 mr-1" />
                              {t('influencers.card.show_less')}
                            </>
                          ) : (
                            <>
                              <ChevronDown className="h-3 w-3 mr-1" />
                              {t('influencers.card.more_networks', { count: networkCount - 3 })}
                            </>
                          )}
                        </Button>
                      </div>
                    )}
                  </div>
                ) : null;
              })()}
            </div>
          </div>


        </div>
        
        {/* Sem conteúdo adicional */}
      </div>
    </Card>
  );
}


